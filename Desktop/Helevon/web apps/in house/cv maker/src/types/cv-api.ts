/**
 * CV API Types
 * 
 * Type definitions for CV data structures used with the FastAPI backend.
 * These replace the Prisma-generated types.
 */

export interface CV {
  id: string;
  user_id: string;
  title: string;
  template: string;
  language: string;
  personal_info: any;
  education: any;
  work_experience: any;
  skills: any;
  references: any;
  cover_letter: any;
  created_at: string;
  updated_at: string;
}

export interface CVFile {
  id: string;
  user_id: string;
  cv_id?: string;
  name: string;
  type: string;
  size: number;
  url: string;
  category: 'photo' | 'certificate' | 'cover_letter' | 'other';
  created_at: string;
  updated_at: string;
}

export interface CVWithFiles extends CV {
  files: CVFile[];
}

// Legacy File type for compatibility
export interface File {
  id: string;
  name: string;
  url: string;
  category: string;
}
