export interface PersonalInfo {
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  postal_code: string;
  country: string;
  date_of_birth?: string;
  place_of_birth?: string;
  nationality?: string;
  marital_status?: string;
  photo_url?: string;
}

export interface EducationEntry {
  id: string;
  institution: string;
  degree: string;
  field: string;
  start_date: string;
  end_date?: string;
  current: boolean;
  location: string;
  description?: string;
  certificate_url?: string;
}

export interface WorkExperienceEntry {
  id: string;
  company: string;
  position: string;
  start_date: string;
  end_date?: string;
  current: boolean;
  location: string;
  description?: string;
}

export interface Skill {
  id: string;
  name: string;
  level: number; // 1-5
  category: 'technical' | 'language' | 'soft';
}

export interface Reference {
  id: string;
  name: string;
  company: string;
  position: string;
  email?: string;
  phone?: string;
  relationship: string;
}

export interface Certificate {
  id: string;
  name: string;
  issuer: string;
  date: string;
  file_url?: string;
  description?: string;
}

export interface CoverLetter {
  // Recipient information
  recipient_name?: string;
  recipient_company?: string;
  recipient_address?: string;
  recipient_city?: string;
  recipient_postal_code?: string; // Always 5 numbers in Germany
  recipient_country?: string;
  recipient_email?: string;
  recipient_phone?: string;
  recipient_other_info?: string;

  // Cover letter content
  subject?: string; // Will be used as the title on the cover page if provided
  content?: string;
  date?: string;
  salutation?: string; // Auto-generated based on recipient_name if provided

  // Signature
  signature_type?: 'text' | 'image'; // Whether to use text or uploaded image
  signature_image_url?: string; // URL to uploaded signature image
}

export interface CVData {
  personal_info: PersonalInfo;
  education: EducationEntry[];
  work_experience: WorkExperienceEntry[];
  skills: Skill[];
  references: Reference[];
  certificates: Certificate[];
  cover_letter?: string | CoverLetter;
}

export type CVTemplate = 'standard' | 'modern' | 'creative' | 'german-ausbildung';

export interface CV {
  id: string;
  user_id: string;
  title: string;
  template: CVTemplate;
  language: string;
  personal_info: PersonalInfo;
  education: EducationEntry[];
  work_experience: WorkExperienceEntry[];
  skills: Skill[];
  references: Reference[];
  certificates: Certificate[];
  cover_letter?: string | CoverLetter;
  created_at: string;
  updated_at: string;
}

export interface FileUpload {
  id: string;
  user_id: string;
  cv_id?: string;
  name: string;
  type: string;
  size: number;
  url: string;
  category: 'photo' | 'certificate' | 'cover_letter' | 'other';
  created_at: string;
  updated_at: string;
}
