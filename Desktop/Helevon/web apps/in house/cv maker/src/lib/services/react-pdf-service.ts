/**
 * React PDF Generation Service
 *
 * This service handles PDF generation for CVs using React PDF.
 */

import React from 'react';
import { CV } from '@/types/cv-api';
import { isPdfExportEnabled } from '@/lib/utils/pdf-export';
import { PersonalInfo, EducationEntry, WorkExperienceEntry, Skill, Reference, CoverLetter } from '@/types/cv';
import { renderToBuffer } from '@react-pdf/renderer';
// Import the CV document component
import { CVDocument } from '@/components/cv/cv-document';
// Import PDF merger utility
import { mergePdfs, extractCertificateUrls, extractCertificatesWithEntries } from '@/lib/utils/pdf-merger';

/**
 * Generate a PDF for a CV using React PDF
 *
 * @param cv The CV to generate a PDF for
 * @param files The files associated with the CV
 * @returns A buffer containing the PDF data or null if PDF generation is disabled
 */
export async function generateReactPdf(cv: CV, files: any[]): Promise<Buffer | null> {
  if (!isPdfExportEnabled()) return null;

  try {
    // Parse CV data with proper type casting
    const personalInfo = (cv.personalInfo as unknown as PersonalInfo) || {};
    const education = (cv.education as unknown as EducationEntry[]) || [];
    const workExperience = (cv.workExperience as unknown as WorkExperienceEntry[]) || [];
    const skills = (cv.skills as unknown as Skill[]) || [];
    const references = (cv.references as unknown as Reference[]) || [];

    // Find the photo file
    const photoFile = files.find(file => file.category === 'photo');
    let photoUrl = photoFile ? photoFile.url : undefined;

    // If the photo URL is an old filesystem path, convert it to the new API route
    if (photoUrl && photoUrl.startsWith('/uploads/')) {
      console.log(`Converting old photo URL to API route: ${photoUrl}`);
      photoUrl = `http://localhost:3000/api/files/by-path?path=${encodeURIComponent(photoUrl)}`;
    } else if (photoUrl && photoUrl.startsWith('/api/')) {
      console.log(`Converting API photo URL to absolute URL: ${photoUrl}`);
      photoUrl = `http://localhost:3000${photoUrl}`;
    } else if (photoUrl) {
      console.log(`Using photo URL: ${photoUrl}`);
    }

    // Process cover letter data
    let coverLetterData: any = null;

    if (cv.coverLetter) {
      if (typeof cv.coverLetter === 'string') {
        try {
          // Try to parse it as JSON
          coverLetterData = JSON.parse(cv.coverLetter);
        } catch (e) {
          // If it's not valid JSON, use it as plain text
          coverLetterData = { content: cv.coverLetter };
        }
      } else if (typeof cv.coverLetter === 'object') {
        // It's already an object
        coverLetterData = cv.coverLetter;
      }
    }

    // Create a modified CV object with properly processed data
    const processedCV = {
      ...cv,
      personalInfo,
      education,
      workExperience,
      skills,
      references,
      coverLetter: coverLetterData
    };

    // Extract target position and field from cover letter if available
    let targetPosition: string | undefined;
    let field: string | undefined;

    if (coverLetterData && coverLetterData.subject) {
      // Try to extract target position from subject
      const subjectMatch = coverLetterData.subject.match(/Bewerbung\s+(?:als|um|für)\s+(?:eine\s+)?(?:Stelle\s+(?:als|zum)|Ausbildung\s+(?:als|zum))?\s+([^\s]+(?:\s+[^\s]+)*)/i);
      if (subjectMatch && subjectMatch[1]) {
        targetPosition = subjectMatch[1];
      }
    }

    // Use default values if not found
    if (!targetPosition) {
      targetPosition = cv.template === 'german-ausbildung' ? 'Fachinformatiker' : undefined;
      field = cv.template === 'german-ausbildung' ? 'Anwendungsentwicklung' : undefined;
    }

    // We'll use the CVDocument component which handles template selection internally

    // Create the PDF document using React components
    const document = React.createElement(CVDocument, {
      cv: processedCV,
      personalInfo,
      education,
      workExperience,
      skills,
      references,
      photoUrl,
      targetPosition,
      field
    });

    // Render the document to a buffer
    try {
      console.log('Rendering PDF with React PDF...');
      const buffer = await renderToBuffer(document);
      console.log('PDF rendered successfully with React PDF');

      // Check if there are any certificates to merge
      const { urls: certificateUrls, entries: certificateEntries } = extractCertificatesWithEntries(education);

      if (certificateUrls.length > 0) {
        console.log(`Found ${certificateUrls.length} certificates to merge`);
        try {
          // Show progress message
          console.log('Starting certificate merging process...');

          // Merge certificates with the main PDF, passing education entries for titles
          const mergedBuffer = await mergePdfs(buffer, certificateUrls, certificateEntries);

          console.log('Successfully merged certificates with main PDF');
          return mergedBuffer;
        } catch (mergeError) {
          console.error('Error merging certificates:', mergeError);
          // If merging fails, return the original buffer
          console.log('Returning original PDF without certificates');
          return buffer;
        }
      } else {
        // No certificates to merge, return the original buffer
        return buffer;
      }
    } catch (renderError) {
      console.error('Error rendering PDF with React PDF:', renderError);
      throw renderError;
    }
  } catch (error) {
    console.error('Error generating PDF with React PDF:', error);

    // If there's an error, return null and let the fallback handle it
    return null;
  }
}
