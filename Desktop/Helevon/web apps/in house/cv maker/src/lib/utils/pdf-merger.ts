/**
 * PDF Merger Utility
 *
 * This utility provides functions to merge PDFs for the CV application.
 */

import { PDFDocument, rgb, StandardFonts } from 'pdf-lib';
import fs from 'fs/promises';
import path from 'path';
import { File } from '@/types/cv-api';
import { EducationEntry } from '@/types/cv';

/**
 * Fetch a PDF file from a URL or file path
 *
 * @param url The URL or file path of the PDF
 * @returns A Uint8Array containing the PDF data
 */
export async function fetchPdf(url: string): Promise<Uint8Array> {
  try {
    // Convert old filesystem paths to API routes
    if (url.startsWith('/uploads/')) {
      // Extract the file ID from the URL (last part of the path)
      const parts = url.split('/');
      const fileName = parts[parts.length - 1];
      const userId = parts[parts.length - 2];

      // Find the CV ID from the context (this is a limitation, but we'll handle it)
      // We'll use a more flexible approach to find the file
      console.log(`Converting old filesystem path to API route: ${url}`);

      // Try to fetch the file data from the database using the prisma client
      try {
        // Import prisma client
        const { prisma } = await import('@/lib/db/prisma');

        // Find the file by name pattern
        const file = await prisma.file.findFirst({
          where: {
            url: {
              contains: fileName
            }
          }
        });

        if (file && file.fileData) {
          console.log(`Found file in database: ${file.name}`);
          // Return the file data directly from the database
          return new Uint8Array(Buffer.from(file.fileData, 'base64'));
        } else {
          console.log(`File not found in database by name: ${fileName}, trying to find by URL pattern`);

          // Try to find by URL pattern
          const fileByUrl = await prisma.file.findFirst({
            where: {
              url: {
                contains: userId
              }
            }
          });

          if (fileByUrl && fileByUrl.fileData) {
            console.log(`Found file in database by URL pattern: ${fileByUrl.name}`);
            return new Uint8Array(Buffer.from(fileByUrl.fileData, 'base64'));
          }
        }
      } catch (dbError) {
        console.error('Error accessing database:', dbError);
      }

      // If we couldn't find the file in the database, try to use the API
      // Convert to an API URL format
      const apiUrl = `/api/files/by-path?path=${encodeURIComponent(url)}`;
      console.log(`Trying API URL: ${apiUrl}`);

      try {
        const response = await fetch(apiUrl);
        if (response.ok) {
          const arrayBuffer = await response.arrayBuffer();
          return new Uint8Array(arrayBuffer);
        }
      } catch (apiError) {
        console.error('Error fetching from API:', apiError);
      }

      throw new Error(`Could not find file data for: ${url}`);
    }

    // Check if the URL is a remote URL, API route, or a local file path
    if (url.startsWith('http')) {
      // Fetch from remote URL
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch PDF from URL: ${url}`);
      }
      const arrayBuffer = await response.arrayBuffer();
      return new Uint8Array(arrayBuffer);
    } else if (url.startsWith('/api/')) {
      // This is an API route for database-stored files
      // Fetch from API route
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch PDF from API route: ${url}`);
      }
      const arrayBuffer = await response.arrayBuffer();
      return new Uint8Array(arrayBuffer);
    } else {
      try {
        // Try to fetch from API first (for database-stored files)
        // Make sure the URL is absolute for fetch to work
        const absoluteUrl = url.startsWith('/') ? `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}${url}` : url;
        console.log(`Trying to fetch from: ${absoluteUrl}`);

        const response = await fetch(absoluteUrl);
        if (response.ok) {
          const arrayBuffer = await response.arrayBuffer();
          return new Uint8Array(arrayBuffer);
        }

        // If we get here, we couldn't fetch the file from the API
        throw new Error(`Could not fetch file from: ${url}`);
      } catch (fetchError) {
        console.error('Error fetching file:', fetchError);
        throw new Error(`Failed to fetch file: ${fetchError.message}`);
      }
    }
  } catch (error) {
    console.error('Error fetching PDF:', error);
    throw new Error(`Failed to fetch PDF: ${error.message}`);
  }
}

/**
 * Merge multiple PDFs into a single PDF
 *
 * @param mainPdfBytes The main PDF document as a Uint8Array
 * @param certificateUrls Array of URLs or file paths to certificate PDFs
 * @param educationEntries Optional array of education entries to get titles from
 * @returns A Buffer containing the merged PDF
 */
export async function mergePdfs(
  mainPdfBytes: Uint8Array,
  certificateUrls: string[],
  educationEntries?: any[]
): Promise<Buffer> {
  try {
    // Load the main PDF document
    const mainPdfDoc = await PDFDocument.load(mainPdfBytes);

    // Get fonts for title headers
    const helveticaBold = await mainPdfDoc.embedFont(StandardFonts.HelveticaBold);
    const helvetica = await mainPdfDoc.embedFont(StandardFonts.Helvetica);

    // Create a map of certificate URLs to education entries for title lookup
    const certificateMap = new Map();
    if (educationEntries && Array.isArray(educationEntries)) {
      educationEntries.forEach(entry => {
        if (entry.certificateUrl) {
          certificateMap.set(entry.certificateUrl, entry);
        }
      });
    }

    // Process each certificate
    for (const url of certificateUrls) {
      try {
        // Only process PDF files
        if (!url.toLowerCase().endsWith('.pdf')) {
          console.log(`Skipping non-PDF file: ${url}`);
          continue;
        }

        console.log(`Processing certificate: ${url}`);

        // Fetch the certificate PDF
        const certificateBytes = await fetchPdf(url);

        if (!certificateBytes || certificateBytes.length === 0) {
          console.error(`Empty or invalid certificate data for: ${url}`);
          continue;
        }

        console.log(`Successfully fetched certificate data (${certificateBytes.length} bytes)`);

        // Load the certificate PDF
        const certificatePdf = await PDFDocument.load(certificateBytes);

        // Get all pages from the certificate PDF
        const certificatePages = await mainPdfDoc.copyPages(
          certificatePdf,
          certificatePdf.getPageIndices()
        );

        // Get education entry for this certificate if available
        const educationEntry = certificateMap.get(url);
        const certificateTitle = educationEntry ?
          `Zertifikat: ${educationEntry.degree} in ${educationEntry.field}` :
          `Zertifikat: ${url.split('/').pop()}`;
        const certificateSubtitle = educationEntry ?
          `${educationEntry.institution}, ${educationEntry.location}` :
          '';

        // Add title page for the certificate
        const titlePage = mainPdfDoc.addPage();
        const { width, height } = titlePage.getSize();

        // Add title
        titlePage.drawText(certificateTitle, {
          x: 50,
          y: height - 100,
          size: 18,
          font: helveticaBold,
          color: rgb(0, 0.35, 0.61), // Professional blue color (#005A9C)
        });

        // Add subtitle if available
        if (certificateSubtitle) {
          titlePage.drawText(certificateSubtitle, {
            x: 50,
            y: height - 130,
            size: 14,
            font: helvetica,
          });
        }

        // Add separator line
        titlePage.drawLine({
          start: { x: 50, y: height - 150 },
          end: { x: width - 50, y: height - 150 },
          thickness: 1,
          color: rgb(0.8, 0.8, 0.8),
        });

        // Add note about the certificate
        titlePage.drawText('Das folgende Zertifikat wird im Original-Format angezeigt:', {
          x: 50,
          y: height - 180,
          size: 12,
          font: helvetica,
        });

        // Add all pages from the certificate to the main PDF
        for (let i = 0; i < certificatePages.length; i++) {
          const page = certificatePages[i];
          const addedPage = mainPdfDoc.addPage(page);

          // Add header to each page of the certificate
          addedPage.drawText(certificateTitle, {
            x: 50,
            y: height - 40,
            size: 10,
            font: helveticaBold,
            color: rgb(0, 0.35, 0.61), // Professional blue color (#005A9C)
          });

          // Add page number for multi-page certificates
          if (certificatePages.length > 1) {
            addedPage.drawText(`Seite ${i + 1} von ${certificatePages.length}`, {
              x: width - 150,
              y: height - 40,
              size: 10,
              font: helvetica,
            });
          }
        }

        console.log(`Successfully merged certificate: ${url}`);
      } catch (certError) {
        console.error(`Error processing certificate ${url}:`, certError);
        // Continue with other certificates even if one fails
      }
    }

    // Save the merged PDF
    const mergedPdfBytes = await mainPdfDoc.save();

    // Convert to Buffer and return
    return Buffer.from(mergedPdfBytes);
  } catch (error) {
    console.error('Error merging PDFs:', error);
    throw new Error(`Failed to merge PDFs: ${error.message}`);
  }
}

/**
 * Extract certificate URLs from education entries
 *
 * @param education Array of education entries
 * @returns Array of certificate URLs
 */
export function extractCertificateUrls(education: any[]): string[] {
  if (!Array.isArray(education)) {
    return [];
  }

  return education
    .filter(entry => entry.certificateUrl)
    .map(entry => {
      // If the URL is an old filesystem path, convert it to the new API route
      if (entry.certificateUrl && entry.certificateUrl.startsWith('/uploads/')) {
        console.log(`Converting old certificate URL: ${entry.certificateUrl}`);
        // We'll handle this in the fetchPdf function
      }
      return entry.certificateUrl;
    });
}

/**
 * Extract certificate URLs and education entries
 *
 * @param education Array of education entries
 * @returns Object with certificate URLs and education entries
 */
export function extractCertificatesWithEntries(education: any[]): { urls: string[], entries: any[] } {
  if (!Array.isArray(education)) {
    return { urls: [], entries: [] };
  }

  const entriesWithCertificates = education.filter(entry => entry.certificateUrl);

  // Process the URLs to ensure they're in the correct format
  const processedUrls = entriesWithCertificates.map(entry => {
    // If the URL is an old filesystem path, we'll handle it in the fetchPdf function
    if (entry.certificateUrl && entry.certificateUrl.startsWith('/uploads/')) {
      console.log(`Found certificate with old filesystem path: ${entry.certificateUrl}`);
    }
    return entry.certificateUrl;
  });

  return {
    urls: processedUrls,
    entries: entriesWithCertificates
  };
}
