/**
 * File Store
 * 
 * Zustand store for managing file upload, retrieval, and management operations
 * for the FastAPI backend integration.
 */

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { api } from '@/lib/api/client';
import { API_CONFIG } from '@/lib/config/api';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/lib/api/error-handler';

export interface FileUpload {
  id: string;
  user_id: string;
  cv_id?: string;
  name: string;
  type: string;
  size: number;
  url: string;
  category: 'photo' | 'certificate' | 'cover_letter' | 'other';
  created_at: string;
  updated_at: string;
}

export interface FileUploadData {
  file_name: string;
  file_data: string; // base64 encoded
  file_type: string;
  category: 'photo' | 'certificate' | 'cover_letter' | 'other';
}

interface FileState {
  // State
  files: FileUpload[];
  isUploading: boolean;
  isLoading: boolean;
  error: string | null;
  uploadProgress: number;
  
  // Actions
  uploadFile: (cvId: string, file: File, category: string) => Promise<FileUpload>;
  fetchFiles: (cvId: string) => Promise<void>;
  deleteFile: (cvId: string, fileId: string) => Promise<void>;
  downloadFile: (cvId: string, fileId: string) => Promise<void>;
  
  // Utility actions
  clearError: () => void;
  reset: () => void;
}

const initialState = {
  files: [],
  isUploading: false,
  isLoading: false,
  error: null,
  uploadProgress: 0,
};

export const useFileStore = create<FileState>()(
  devtools(
    (set, get) => ({
      ...initialState,

      // Upload a file
      uploadFile: async (cvId: string, file: File, category: string) => {
        set({ isUploading: true, error: null, uploadProgress: 0 });
        
        try {
          // Convert file to base64
          const base64 = await new Promise<string>((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => {
              const result = reader.result as string;
              // Remove data:mime/type;base64, prefix
              const base64Data = result.split(',')[1];
              resolve(base64Data);
            };
            reader.onerror = reject;
            reader.readAsDataURL(file);
          });

          set({ uploadProgress: 50 });

          const uploadData: FileUploadData = {
            file_name: file.name,
            file_data: base64,
            file_type: file.type,
            category: category as 'photo' | 'certificate' | 'cover_letter' | 'other',
          };

          const uploadedFile = await api.post<FileUpload>(
            API_CONFIG.ENDPOINTS.CV.UPLOAD(cvId),
            uploadData
          );

          set({ uploadProgress: 100 });

          // Update files list
          const { files } = get();
          set({
            files: [uploadedFile, ...files],
            isUploading: false,
            uploadProgress: 0,
          });

          return uploadedFile;
        } catch (error) {
          const errorNotification = ErrorHandler.handleApiError(error);
          set({
            error: errorNotification.message || errorNotification.title,
            isUploading: false,
            uploadProgress: 0,
          });
          throw error;
        }
      },

      // Fetch files for a CV
      fetchFiles: async (cvId: string) => {
        set({ isLoading: true, error: null });
        
        try {
          const files = await api.get<FileUpload[]>(API_CONFIG.ENDPOINTS.CV.FILES(cvId));
          set({ files, isLoading: false });
        } catch (error) {
          const errorNotification = ErrorHandler.handleApiError(error);
          set({
            error: errorNotification.message || errorNotification.title,
            isLoading: false,
          });
        }
      },

      // Delete a file
      deleteFile: async (cvId: string, fileId: string) => {
        set({ isLoading: true, error: null });
        
        try {
          await api.delete(API_CONFIG.ENDPOINTS.CV.FILE(cvId, fileId));
          
          // Remove file from local state
          const { files } = get();
          const updatedFiles = files.filter(file => file.id !== fileId);
          set({ files: updatedFiles, isLoading: false });
        } catch (error) {
          const errorNotification = ErrorHandler.handleApiError(error);
          set({
            error: errorNotification.message || errorNotification.title,
            isLoading: false,
          });
        }
      },

      // Download a file
      downloadFile: async (cvId: string, fileId: string) => {
        set({ isLoading: true, error: null });
        
        try {
          const response = await fetch(
            `${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.CV.FILE(cvId, fileId)}`,
            {
              headers: {
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
              },
            }
          );

          if (!response.ok) {
            throw new Error('Failed to download file');
          }

          const blob = await response.blob();
          const url = window.URL.createObjectURL(blob);
          
          // Get filename from Content-Disposition header or use default
          const contentDisposition = response.headers.get('Content-Disposition');
          let filename = 'download';
          if (contentDisposition) {
            const filenameMatch = contentDisposition.match(/filename="(.+)"/);
            if (filenameMatch) {
              filename = filenameMatch[1];
            }
          }

          // Create download link
          const link = document.createElement('a');
          link.href = url;
          link.download = filename;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          
          // Clean up
          window.URL.revokeObjectURL(url);
          
          set({ isLoading: false });
        } catch (error) {
          const errorNotification = ErrorHandler.handleApiError(error);
          set({
            error: errorNotification.message || errorNotification.title,
            isLoading: false,
          });
        }
      },

      // Clear error
      clearError: () => {
        set({ error: null });
      },

      // Reset store
      reset: () => {
        set(initialState);
      },
    }),
    {
      name: 'file-store',
    }
  )
);

/**
 * Utility function to validate file types
 */
export const validateFileType = (file: File, category: string): boolean => {
  const allowedTypes: Record<string, string[]> = {
    photo: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'],
    certificate: ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'],
    cover_letter: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
    other: ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
  };

  return allowedTypes[category]?.includes(file.type) || false;
};

/**
 * Utility function to format file size
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Utility function to get file icon based on type
 */
export const getFileIcon = (fileType: string): string => {
  if (fileType.startsWith('image/')) return '🖼️';
  if (fileType === 'application/pdf') return '📄';
  if (fileType.includes('word')) return '📝';
  return '📎';
};
