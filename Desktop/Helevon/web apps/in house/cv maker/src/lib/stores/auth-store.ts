/**
 * Authentication Store
 * 
 * Zustand store for managing JWT tokens, user authentication state,
 * and authentication actions for FastAPI backend integration.
 */

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { api, TokenManager } from '@/lib/api/client';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/lib/api/error-handler';

export interface User {
  id: string;
  name: string;
  email: string;
  language: string;
  created_at: string;
  updated_at: string;
  last_login_at?: string;
  email_verified?: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  name: string;
  email: string;
  password: string;
  confirm_password: string;
  language: string;
}

interface AuthState {
  // State
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  
  // Token state
  accessToken: string | null;
  refreshToken: string | null;
  tokenExpiresAt: number | null;
  
  // Actions
  login: (credentials: LoginCredentials) => Promise<boolean>;
  register: (data: RegisterData) => Promise<boolean>;
  logout: () => Promise<void>;
  refreshTokens: () => Promise<boolean>;
  
  // User management
  fetchUserProfile: () => Promise<void>;
  updateUserAccount: (data: Partial<User>) => Promise<boolean>;
  
  // Utility actions
  setUser: (user: User | null) => void;
  setTokens: (accessToken: string, refreshToken: string, expiresIn: number) => void;
  clearAuth: () => void;
  clearError: () => void;
  
  // Initialization
  initialize: () => Promise<void>;
}

const initialState = {
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
  accessToken: null,
  refreshToken: null,
  tokenExpiresAt: null,
};

export const useAuthStore = create<AuthState>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,

        // Login user
        login: async (credentials: LoginCredentials) => {
          set({ isLoading: true, error: null });
          
          try {
            const response = await api.login(credentials.email, credentials.password);
            
            // Store tokens
            TokenManager.setTokens(response.access_token, response.refresh_token);
            
            // Calculate expiration time
            const expiresAt = Date.now() + (response.expires_in * 1000);
            
            set({
              accessToken: response.access_token,
              refreshToken: response.refresh_token,
              tokenExpiresAt: expiresAt,
              isAuthenticated: true,
              isLoading: false,
            });
            
            // Fetch user profile
            await get().fetchUserProfile();
            
            return true;
          } catch (error) {
            const errorNotification = ErrorHandler.handleApiError(error);
            set({ 
              error: errorNotification.message || errorNotification.title,
              isLoading: false,
              isAuthenticated: false,
            });
            return false;
          }
        },

        // Register new user
        register: async (data: RegisterData) => {
          set({ isLoading: true, error: null });
          
          try {
            await api.register(data);
            set({ isLoading: false });
            return true;
          } catch (error) {
            const errorNotification = ErrorHandler.handleApiError(error);
            set({ 
              error: errorNotification.message || errorNotification.title,
              isLoading: false,
            });
            return false;
          }
        },

        // Logout user
        logout: async () => {
          set({ isLoading: true });
          
          try {
            await api.logout();
          } catch (error) {
            console.error('Logout error:', error);
          } finally {
            TokenManager.clearTokens();
            set({
              ...initialState,
              isLoading: false,
            });
          }
        },

        // Refresh access token
        refreshTokens: async () => {
          const { refreshToken } = get();
          if (!refreshToken) {
            return false;
          }

          try {
            const response = await fetch('/api/v1/auth/refresh-token', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({ refresh_token: refreshToken }),
            });

            if (!response.ok) {
              get().clearAuth();
              return false;
            }

            const data = await response.json();
            TokenManager.setTokens(data.access_token, data.refresh_token);
            
            const expiresAt = Date.now() + (data.expires_in * 1000);
            
            set({
              accessToken: data.access_token,
              refreshToken: data.refresh_token,
              tokenExpiresAt: expiresAt,
            });

            return true;
          } catch (error) {
            console.error('Token refresh error:', error);
            get().clearAuth();
            return false;
          }
        },

        // Fetch user profile
        fetchUserProfile: async () => {
          try {
            const user = await api.get<User>('/api/v1/user/account');
            set({ user });
          } catch (error) {
            console.error('Failed to fetch user profile:', error);
            const errorNotification = ErrorHandler.handleApiError(error);
            set({ error: errorNotification.message || errorNotification.title });
          }
        },

        // Update user account
        updateUserAccount: async (data: Partial<User>) => {
          set({ isLoading: true, error: null });
          
          try {
            const updatedUser = await api.put<User>('/api/v1/user/account', data);
            set({ 
              user: updatedUser,
              isLoading: false,
            });
            return true;
          } catch (error) {
            const errorNotification = ErrorHandler.handleApiError(error);
            set({ 
              error: errorNotification.message || errorNotification.title,
              isLoading: false,
            });
            return false;
          }
        },

        // Set user
        setUser: (user: User | null) => {
          set({ user });
        },

        // Set tokens
        setTokens: (accessToken: string, refreshToken: string, expiresIn: number) => {
          const expiresAt = Date.now() + (expiresIn * 1000);
          TokenManager.setTokens(accessToken, refreshToken);
          set({
            accessToken,
            refreshToken,
            tokenExpiresAt: expiresAt,
            isAuthenticated: true,
          });
        },

        // Clear authentication
        clearAuth: () => {
          TokenManager.clearTokens();
          set({
            ...initialState,
          });
        },

        // Clear error
        clearError: () => {
          set({ error: null });
        },

        // Initialize store
        initialize: async () => {
          const accessToken = TokenManager.getAccessToken();
          const refreshToken = TokenManager.getRefreshToken();
          
          if (accessToken && refreshToken) {
            // Check if token is expired
            if (TokenManager.isTokenExpired(accessToken)) {
              // Try to refresh
              const refreshed = await get().refreshTokens();
              if (!refreshed) {
                get().clearAuth();
                return;
              }
            } else {
              set({
                accessToken,
                refreshToken,
                isAuthenticated: true,
              });
            }
            
            // Fetch user profile
            await get().fetchUserProfile();
          }
        },
      }),
      {
        name: 'auth-store',
        partialize: (state) => ({
          user: state.user,
          accessToken: state.accessToken,
          refreshToken: state.refreshToken,
          tokenExpiresAt: state.tokenExpiresAt,
          isAuthenticated: state.isAuthenticated,
        }),
      }
    ),
    {
      name: 'auth-store',
    }
  )
);
