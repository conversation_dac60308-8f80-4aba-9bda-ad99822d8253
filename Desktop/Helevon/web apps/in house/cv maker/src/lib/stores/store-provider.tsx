/**
 * Store Provider
 * 
 * Provides centralized store initialization and management for the application.
 * This provider initializes stores based on authentication state and manages
 * periodic health checks and data synchronization.
 */

'use client';

import React, { createContext, useContext, useEffect, ReactNode } from 'react';
import { useAuthStore } from './auth-store';
import { useCVStore } from './cv-store';
import { useUserStore } from './user-store';
import { useAppStore } from './app-store';

interface StoreContextType {
  isInitialized: boolean;
}

const StoreContext = createContext<StoreContextType>({
  isInitialized: false,
});

export const useStoreContext = () => {
  const context = useContext(StoreContext);
  if (!context) {
    throw new Error('useStoreContext must be used within a StoreProvider');
  }
  return context;
};

interface StoreProviderProps {
  children: ReactNode;
}

export function StoreProvider({ children }: StoreProviderProps) {
  const { user, isAuthenticated, isLoading: authLoading } = useAuthStore();
  const [isInitialized, setIsInitialized] = React.useState(false);
  
  // Store actions
  const setProfile = useUserStore((state) => state.setProfile);
  const fetchProfile = useUserStore((state) => state.fetchProfile);
  const checkHealth = useAppStore((state) => state.checkHealth);
  const resetCVStore = useCVStore((state) => state.reset);
  const resetUserStore = useUserStore((state) => state.reset);

  // Initialize stores based on authentication state
  useEffect(() => {
    const initializeStores = async () => {
      try {
        // Always check health first
        await checkHealth();

        if (isAuthenticated && user) {
          // User is authenticated, set profile data
          setProfile({
            id: user.id,
            name: user.name || user.email || '',
            email: user.email,
            language: user.language || 'en',
            created_at: user.created_at || new Date().toISOString(),
            updated_at: user.updated_at || new Date().toISOString(),
            last_login_at: user.last_login_at,
          });

          // Fetch full user profile data
          try {
            await fetchProfile();
          } catch (error) {
            console.error('Failed to fetch user profile:', error);
          }
        } else {
          // User is not authenticated, reset stores
          resetCVStore();
          resetUserStore();
        }
      } catch (error) {
        console.error('Failed to initialize stores:', error);
      } finally {
        setIsInitialized(true);
      }
    };

    // Only initialize when auth loading is complete
    if (!authLoading) {
      initializeStores();
    }
  }, [user, isAuthenticated, authLoading, setProfile, fetchProfile, checkHealth, resetCVStore, resetUserStore]);

  // Periodic health check
  useEffect(() => {
    const interval = setInterval(() => {
      checkHealth();
    }, 60000); // Check every minute

    return () => clearInterval(interval);
  }, [checkHealth]);

  // Show loading state while initializing
  if (!isInitialized || authLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <StoreContext.Provider value={{ isInitialized }}>
      {children}
    </StoreContext.Provider>
  );
}

/**
 * Hook to check if stores are initialized
 */
export const useStoresInitialized = () => {
  const { isInitialized } = useStoreContext();
  return isInitialized;
};

/**
 * Higher-order component to ensure stores are initialized
 */
export function withStoreInitialization<P extends object>(
  Component: React.ComponentType<P>
) {
  return function WithStoreInitializationComponent(props: P) {
    const isInitialized = useStoresInitialized();
    
    if (!isInitialized) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
        </div>
      );
    }
    
    return <Component {...props} />;
  };
}
