'use client';

import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { Menu, X } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { useAuthStore } from '@/lib/stores/auth-store';
import { useTranslation } from '@/lib/i18n/translation-context';
import { ThemeAwareLogo } from './theme-aware-logo';
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
  SheetDescription,
} from '@/components/ui/sheet';

interface NavItem {
  title: string;
  href: string;
  disabled?: boolean;
}

export function MainNav() {
  const pathname = usePathname();
  const router = useRouter();
  const { user, logout } = useAuthStore();
  const { t } = useTranslation();

  // Redirect to dashboard if user is logged in and trying to access home page
  useEffect(() => {
    if (user && pathname === '/') {
      router.push('/dashboard');
    }
  }, [user, pathname, router]);

  const navItems: NavItem[] = [];

  // Add authenticated routes
  if (session) {
    navItems.push(
      {
        title: t('nav.dashboard'),
        href: '/dashboard',
      },
      {
        title: t('nav.editor'),
        href: '/cv/new',
      },
      {
        title: t('nav.account'),
        href: '/account',
      }
    );
  } else {
    // Add unauthenticated routes
    navItems.push(
      {
        title: t('nav.home'),
        href: '/',
      },
      {
        title: t('nav.login'),
        href: '/login',
      },
      {
        title: t('nav.register'),
        href: '/register',
      }
    );
  }

  return (
    <div className="flex items-center justify-between w-full">
      <Link href={session ? "/dashboard" : "/"} className="flex items-center space-x-2">
        <span className="font-bold">{t('app.name')} by</span>
        <ThemeAwareLogo height={8} width={32} />
      </Link>

      {/* Mobile Navigation */}
      <div className="md:hidden">
        <Sheet>
          <SheetTrigger asChild>
            <Button variant="ghost" size="icon" className="h-9 w-9 p-0">
              <Menu className="h-5 w-5" />
              <span className="sr-only">Toggle menu</span>
            </Button>
          </SheetTrigger>
          <SheetContent side="left">
            <SheetHeader>
              <SheetTitle className="flex items-center gap-2">
                <span>{t('app.name')} by</span>
                <ThemeAwareLogo height={7} width={28} />
              </SheetTitle>
            </SheetHeader>
            <div className="flex flex-col gap-4 py-4">
              {navItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className={cn(
                    'text-sm font-medium transition-colors hover:text-foreground/80',
                    pathname === item.href
                      ? 'text-foreground font-bold'
                      : 'text-foreground/60',
                    item.disabled && 'cursor-not-allowed opacity-80'
                  )}
                >
                  {item.title}
                </Link>
              ))}
              {user && (
                <Button
                  variant="ghost"
                  className="justify-start p-0 text-sm font-medium text-foreground/60 hover:text-foreground/80"
                  onClick={() => logout()}
                >
                  {t('nav.logout')}
                </Button>
              )}
            </div>
          </SheetContent>
        </Sheet>
      </div>

      {/* Desktop Navigation */}
      <nav className="hidden md:flex items-center gap-6">
        {navItems.map((item) => (
          <Link
            key={item.href}
            href={item.href}
            className={cn(
              'flex items-center text-sm font-medium transition-colors hover:text-foreground/80 relative',
              pathname === item.href
                ? 'text-foreground after:absolute after:bottom-[-18px] after:left-0 after:h-[2px] after:w-full after:bg-foreground'
                : 'text-foreground/60',
              item.disabled && 'cursor-not-allowed opacity-80'
            )}
          >
            {item.title}
          </Link>
        ))}
        {user && (
          <Button
            variant="ghost"
            className="text-sm font-medium text-foreground/60 hover:text-foreground/80"
            onClick={() => logout()}
          >
            {t('nav.logout')}
          </Button>
        )}
      </nav>
    </div>
  );
}
