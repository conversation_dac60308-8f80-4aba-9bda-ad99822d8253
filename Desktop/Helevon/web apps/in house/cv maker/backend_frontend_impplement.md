# CV Maker API - Frontend Integration Guide

This document provides complete API route documentation for frontend integration with the CV Maker backend.

## Base URL
```
Development: http://localhost:8000
Production: https://your-domain.com
```

## Authentication Headers
For protected routes, include the JW<PERSON> token in the Authorization header:
```
Authorization: Bearer <access_token>
```

---

## 🏥 Health & Status

### Category: Health Check
**Route:** `/health`  
**Request Type:** GET  
**Payload:** None  
**Sample Response:**
```json
{
  "status": "healthy",
  "service": "cv-maker-api"
}
```

### Category: Root Endpoint
**Route:** `/`  
**Request Type:** GET  
**Payload:** None  
**Sample Response:**
```json
{
  "message": "CV Maker API is running",
  "version": "1.0.0"
}
```

---

## 🔐 Authentication

### Category: User Registration
**Route:** `/api/v1/auth/register`  
**Request Type:** POST  
**Payload:**
```json
{
  "name": "<PERSON>",
  "email": "<EMAIL>",
  "password": "securepassword123",
  "confirm_password": "securepassword123",
  "language": "en"
}
```
**Sample Response:**
```json
{
  "id": "550e8400-e29b-41d4-a716-************",
  "name": "John Doe",
  "email": "<EMAIL>",
  "language": "en",
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:30:00Z",
  "last_login_at": null,
  "email_verified": null
}
```

### Category: User Login
**Route:** `/api/v1/auth/signin`  
**Request Type:** POST  
**Payload:**
```json
{
  "email": "<EMAIL>",
  "password": "securepassword123"
}
```
**Sample Response:**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 1800
}
```

### Category: Token Refresh
**Route:** `/api/v1/auth/refresh-token`  
**Request Type:** POST  
**Payload:**
```json
{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```
**Sample Response:**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 1800
}
```

### Category: User Logout
**Route:** `/api/v1/auth/signout`  
**Request Type:** POST  
**Payload:** None (requires Authorization header)  
**Sample Response:**
```json
{
  "message": "Successfully logged out"
}
```

### Category: Password Verification
**Route:** `/api/v1/auth/verify-password`  
**Request Type:** POST  
**Payload:**
```json
{
  "password": "plainpassword",
  "hashed_password": "$2b$12$..."
}
```
**Sample Response:**
```json
{
  "is_valid": true
}
```

---

## 👤 User Management

### Category: Get User Account
**Route:** `/api/v1/user/account`  
**Request Type:** GET  
**Payload:** None (requires Authorization header)  
**Sample Response:**
```json
{
  "id": "550e8400-e29b-41d4-a716-************",
  "name": "John Doe",
  "email": "<EMAIL>",
  "language": "en",
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:30:00Z",
  "last_login_at": "2024-01-15T14:20:00Z",
  "email_verified": null
}
```

### Category: Update User Account
**Route:** `/api/v1/user/account`  
**Request Type:** PUT  
**Payload:**
```json
{
  "name": "John Smith",
  "email": "<EMAIL>",
  "language": "de",
  "current_password": "oldpassword123",
  "new_password": "newpassword123",
  "confirm_password": "newpassword123"
}
```
**Sample Response:**
```json
{
  "id": "550e8400-e29b-41d4-a716-************",
  "name": "John Smith",
  "email": "<EMAIL>",
  "language": "de",
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T15:45:00Z",
  "last_login_at": "2024-01-15T14:20:00Z",
  "email_verified": null
}
```

### Category: Get User Profile
**Route:** `/api/v1/user/profile`  
**Request Type:** GET  
**Payload:** None (requires Authorization header)  
**Sample Response:**
```json
{
  "id": "550e8400-e29b-41d4-a716-************",
  "name": "John Doe",
  "email": "<EMAIL>",
  "language": "en",
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:30:00Z",
  "last_login_at": "2024-01-15T14:20:00Z",
  "email_verified": null,
  "login_attempts": 0,
  "locked_until": null
}
```

### Category: Delete User Account
**Route:** `/api/v1/user/account`  
**Request Type:** DELETE  
**Payload:** None (requires Authorization header)  
**Sample Response:**
```json
{
  "message": "Account successfully deleted"
}
```

### Category: Unlock User Account
**Route:** `/api/v1/user/unlock-account`  
**Request Type:** POST  
**Payload:** None (requires Authorization header)  
**Sample Response:**
```json
{
  "message": "Account successfully unlocked"
}
```

---

## 📄 CV Management

### Category: Create CV
**Route:** `/api/v1/cv`  
**Request Type:** POST  
**Payload:**
```json
{
  "title": "Software Developer CV",
  "template": "modern",
  "language": "en",
  "user_id": "550e8400-e29b-41d4-a716-************"
}
```
**Sample Response:**
```json
{
  "id": "cv-550e8400-e29b-41d4-a716-************",
  "user_id": "550e8400-e29b-41d4-a716-************",
  "title": "Software Developer CV",
  "template": "modern",
  "language": "en",
  "personal_info": {},
  "education": [],
  "work_experience": [],
  "skills": [],
  "references": [],
  "cover_letter": null,
  "created_at": "2024-01-15T16:00:00Z",
  "updated_at": "2024-01-15T16:00:00Z"
}
```

### Category: List User CVs
**Route:** `/api/v1/cv`  
**Request Type:** GET  
**Payload:** None (requires Authorization header)  
**Sample Response:**
```json
[
  {
    "id": "cv-550e8400-e29b-41d4-a716-************",
    "user_id": "550e8400-e29b-41d4-a716-************",
    "title": "Software Developer CV",
    "template": "modern",
    "language": "en",
    "created_at": "2024-01-15T16:00:00Z",
    "updated_at": "2024-01-15T16:00:00Z"
  }
]
```

### Category: Get Specific CV
**Route:** `/api/v1/cv/{cv_id}`  
**Request Type:** GET  
**Payload:** None (requires Authorization header)  
**Sample Response:**
```json
{
  "id": "cv-550e8400-e29b-41d4-a716-************",
  "user_id": "550e8400-e29b-41d4-a716-************",
  "title": "Software Developer CV",
  "template": "modern",
  "language": "en",
  "personal_info": {
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "address": "123 Main St",
    "city": "New York",
    "postalCode": "10001",
    "country": "USA"
  },
  "education": [],
  "work_experience": [],
  "skills": [],
  "references": [],
  "cover_letter": null,
  "files": [
    {
      "id": "file-123",
      "name": "profile.jpg",
      "url": "/api/v1/cv/cv-550e8400-e29b-41d4-a716-************/file/file-123",
      "category": "photo"
    }
  ],
  "created_at": "2024-01-15T16:00:00Z",
  "updated_at": "2024-01-15T16:00:00Z"
}
```

### Category: Update CV
**Route:** `/api/v1/cv/{cv_id}`
**Request Type:** PUT
**Payload:**
```json
{
  "title": "Senior Software Developer CV",
  "template": "creative",
  "language": "de"
}
```
**Sample Response:**
```json
{
  "id": "cv-550e8400-e29b-41d4-a716-************",
  "user_id": "550e8400-e29b-41d4-a716-************",
  "title": "Senior Software Developer CV",
  "template": "creative",
  "language": "de",
  "personal_info": {},
  "education": [],
  "work_experience": [],
  "skills": [],
  "references": [],
  "cover_letter": null,
  "created_at": "2024-01-15T16:00:00Z",
  "updated_at": "2024-01-15T16:30:00Z"
}
```

### Category: Delete CV
**Route:** `/api/v1/cv/{cv_id}`
**Request Type:** DELETE
**Payload:** None (requires Authorization header)
**Sample Response:**
```json
{
  "message": "CV successfully deleted"
}
```

---

## 📝 CV Section Updates

### Category: Update Personal Information
**Route:** `/api/v1/cv/{cv_id}/personal-info`
**Request Type:** PUT
**Payload:**
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "phone": "+1234567890",
  "address": "123 Main St",
  "city": "New York",
  "postalCode": "10001",
  "country": "USA",
  "dateOfBirth": "1990-05-15",
  "placeOfBirth": "Boston, MA",
  "nationality": "American",
  "maritalStatus": "Single",
  "photoUrl": "/api/v1/cv/cv-123/file/photo-456"
}
```
**Sample Response:**
```json
{
  "id": "cv-550e8400-e29b-41d4-a716-************",
  "personal_info": {
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "address": "123 Main St",
    "city": "New York",
    "postalCode": "10001",
    "country": "USA",
    "dateOfBirth": "1990-05-15",
    "placeOfBirth": "Boston, MA",
    "nationality": "American",
    "maritalStatus": "Single",
    "photoUrl": "/api/v1/cv/cv-123/file/photo-456"
  },
  "updated_at": "2024-01-15T16:45:00Z"
}
```

### Category: Update Education
**Route:** `/api/v1/cv/{cv_id}/education`
**Request Type:** PUT
**Payload:**
```json
{
  "education": [
    {
      "id": 1,
      "institution": "MIT",
      "degree": "Bachelor of Science",
      "fieldOfStudy": "Computer Science",
      "startDate": "2008-09-01",
      "endDate": "2012-06-01",
      "grade": "3.8 GPA",
      "description": "Focused on algorithms and software engineering",
      "certificateIds": [1, 2]
    }
  ]
}
```
**Sample Response:**
```json
{
  "id": "cv-550e8400-e29b-41d4-a716-************",
  "education": [
    {
      "id": 1,
      "institution": "MIT",
      "degree": "Bachelor of Science",
      "fieldOfStudy": "Computer Science",
      "startDate": "2008-09-01",
      "endDate": "2012-06-01",
      "grade": "3.8 GPA",
      "description": "Focused on algorithms and software engineering",
      "certificateIds": [1, 2]
    }
  ],
  "updated_at": "2024-01-15T17:00:00Z"
}
```

### Category: Update Work Experience
**Route:** `/api/v1/cv/{cv_id}/work-experience`
**Request Type:** PUT
**Payload:**
```json
{
  "work_experience": [
    {
      "id": 1,
      "company": "Tech Corp",
      "position": "Senior Software Engineer",
      "startDate": "2020-01-01",
      "endDate": "2024-01-01",
      "description": "Led development of microservices architecture",
      "location": "San Francisco, CA",
      "isCurrentJob": false
    }
  ]
}
```
**Sample Response:**
```json
{
  "id": "cv-550e8400-e29b-41d4-a716-************",
  "work_experience": [
    {
      "id": 1,
      "company": "Tech Corp",
      "position": "Senior Software Engineer",
      "startDate": "2020-01-01",
      "endDate": "2024-01-01",
      "description": "Led development of microservices architecture",
      "location": "San Francisco, CA",
      "isCurrentJob": false
    }
  ],
  "updated_at": "2024-01-15T17:15:00Z"
}
```

### Category: Update Skills
**Route:** `/api/v1/cv/{cv_id}/skills`
**Request Type:** PUT
**Payload:**
```json
{
  "skills": [
    {
      "id": 1,
      "name": "Python",
      "level": "Expert",
      "category": "Programming Languages"
    },
    {
      "id": 2,
      "name": "React",
      "level": "Advanced",
      "category": "Frontend Frameworks"
    }
  ]
}
```
**Sample Response:**
```json
{
  "id": "cv-550e8400-e29b-41d4-a716-************",
  "skills": [
    {
      "id": 1,
      "name": "Python",
      "level": "Expert",
      "category": "Programming Languages"
    },
    {
      "id": 2,
      "name": "React",
      "level": "Advanced",
      "category": "Frontend Frameworks"
    }
  ],
  "updated_at": "2024-01-15T17:30:00Z"
}
```

### Category: Update References
**Route:** `/api/v1/cv/{cv_id}/references`
**Request Type:** PUT
**Payload:**
```json
{
  "references": [
    {
      "id": 1,
      "name": "Jane Smith",
      "position": "Engineering Manager",
      "company": "Tech Corp",
      "email": "<EMAIL>",
      "phone": "+1987654321",
      "relationship": "Former Manager"
    }
  ]
}
```
**Sample Response:**
```json
{
  "id": "cv-550e8400-e29b-41d4-a716-************",
  "references": [
    {
      "id": 1,
      "name": "Jane Smith",
      "position": "Engineering Manager",
      "company": "Tech Corp",
      "email": "<EMAIL>",
      "phone": "+1987654321",
      "relationship": "Former Manager"
    }
  ],
  "updated_at": "2024-01-15T17:45:00Z"
}
```

### Category: Update Cover Letter
**Route:** `/api/v1/cv/{cv_id}/cover-letter`
**Request Type:** PUT
**Payload:**
```json
{
  "cover_letter": "Dear Hiring Manager,\n\nI am writing to express my interest in the Software Engineer position..."
}
```
**Sample Response:**
```json
{
  "id": "cv-550e8400-e29b-41d4-a716-************",
  "cover_letter": "Dear Hiring Manager,\n\nI am writing to express my interest in the Software Engineer position...",
  "updated_at": "2024-01-15T18:00:00Z"
}
```

---

## 📁 File Management

### Category: Upload File
**Route:** `/api/v1/cv/{cv_id}/upload`
**Request Type:** POST
**Payload:**
```json
{
  "file_name": "profile_photo.jpg",
  "file_data": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD...",
  "category": "photo"
}
```
**Sample Response:**
```json
{
  "id": "file-550e8400-e29b-41d4-a716-446655440002",
  "cv_id": "cv-550e8400-e29b-41d4-a716-************",
  "user_id": "550e8400-e29b-41d4-a716-************",
  "name": "profile_photo.jpg",
  "type": "image/jpeg",
  "size": 45678,
  "url": "/api/v1/cv/cv-550e8400-e29b-41d4-a716-************/file/file-550e8400-e29b-41d4-a716-446655440002",
  "category": "photo",
  "created_at": "2024-01-15T18:15:00Z",
  "updated_at": "2024-01-15T18:15:00Z"
}
```

### Category: Get File
**Route:** `/api/v1/cv/{cv_id}/file/{file_id}`
**Request Type:** GET
**Payload:** None (requires Authorization header)
**Sample Response:**
```
Content-Type: image/jpeg
Content-Disposition: attachment; filename="profile_photo.jpg"

[Binary file data]
```

### Category: List CV Files
**Route:** `/api/v1/cv/{cv_id}/files`
**Request Type:** GET
**Payload:** None (requires Authorization header)
**Sample Response:**
```json
[
  {
    "id": "file-550e8400-e29b-41d4-a716-446655440002",
    "cv_id": "cv-550e8400-e29b-41d4-a716-************",
    "user_id": "550e8400-e29b-41d4-a716-************",
    "name": "profile_photo.jpg",
    "type": "image/jpeg",
    "size": 45678,
    "url": "/api/v1/cv/cv-550e8400-e29b-41d4-a716-************/file/file-550e8400-e29b-41d4-a716-446655440002",
    "category": "photo",
    "created_at": "2024-01-15T18:15:00Z",
    "updated_at": "2024-01-15T18:15:00Z"
  }
]
```

### Category: Delete File
**Route:** `/api/v1/cv/{cv_id}/file/{file_id}`
**Request Type:** DELETE
**Payload:** None (requires Authorization header)
**Sample Response:**
```json
{
  "message": "File successfully deleted"
}
```

---

## 📄 PDF Export

### Category: Export CV as PDF
**Route:** `/api/v1/cv/{cv_id}/export`
**Request Type:** GET
**Payload:** None (requires Authorization header)
**Sample Response:**
```
Content-Type: application/pdf
Content-Disposition: attachment; filename="John_Doe_CV.pdf"

[PDF binary data]
```

---

## 🏆 Certificate Management

### Category: Upload Certificate
**Route:** `/api/v1/certificates/upload`
**Request Type:** POST
**Payload:**
```json
{
  "education_entry_id": 1,
  "file_name": "diploma.pdf",
  "file_data": "data:application/pdf;base64,JVBERi0xLjQKJcOkw7zDtsO..."
}
```
**Sample Response:**
```json
{
  "id": 1,
  "user_id": "550e8400-e29b-41d4-a716-************",
  "education_entry_id": 1,
  "file_name": "diploma.pdf",
  "file_type": "application/pdf",
  "file_size": 123456,
  "is_deleted": false,
  "created_at": "2024-01-15T18:30:00Z",
  "updated_at": "2024-01-15T18:30:00Z"
}
```

### Category: Get Certificate
**Route:** `/api/v1/certificates/{certificate_id}`
**Request Type:** GET
**Payload:** None (requires Authorization header)
**Sample Response:**
```
Content-Type: application/pdf
Content-Disposition: attachment; filename="diploma.pdf"

[PDF binary data]
```

### Category: List User Certificates
**Route:** `/api/v1/certificates`
**Request Type:** GET
**Payload:** None (requires Authorization header)
**Sample Response:**
```json
[
  {
    "id": 1,
    "user_id": "550e8400-e29b-41d4-a716-************",
    "education_entry_id": 1,
    "file_name": "diploma.pdf",
    "file_type": "application/pdf",
    "file_size": 123456,
    "is_deleted": false,
    "created_at": "2024-01-15T18:30:00Z",
    "updated_at": "2024-01-15T18:30:00Z"
  }
]
```

### Category: Delete Certificate
**Route:** `/api/v1/certificates/{certificate_id}`
**Request Type:** DELETE
**Payload:** None (requires Authorization header)
**Sample Response:**
```json
{
  "message": "Certificate successfully deleted"
}
```

---

## 📊 Analytics & Metrics

### Category: Login Frequency Metrics
**Route:** `/api/v1/metrics/login-frequencies`
**Request Type:** GET
**Payload:** None (requires Authorization header)
**Sample Response:**
```json
{
  "daily_logins": [
    {
      "date": "2024-01-15",
      "count": 5
    }
  ],
  "weekly_logins": [
    {
      "week": "2024-W03",
      "count": 25
    }
  ],
  "monthly_logins": [
    {
      "month": "2024-01",
      "count": 100
    }
  ]
}
```

### Category: Action Frequency Metrics
**Route:** `/api/v1/metrics/action-frequencies`
**Request Type:** GET
**Payload:** None (requires Authorization header)
**Sample Response:**
```json
{
  "action_counts": [
    {
      "action_type": "create_cv",
      "count": 15
    },
    {
      "action_type": "update_cv",
      "count": 45
    },
    {
      "action_type": "export_pdf",
      "count": 30
    }
  ]
}
```

### Category: Popular Actions
**Route:** `/api/v1/metrics/popular-actions`
**Request Type:** GET
**Payload:** None (requires Authorization header)
**Sample Response:**
```json
{
  "popular_actions": [
    {
      "action_type": "update_cv",
      "count": 45,
      "percentage": 45.0
    },
    {
      "action_type": "export_pdf",
      "count": 30,
      "percentage": 30.0
    },
    {
      "action_type": "create_cv",
      "count": 15,
      "percentage": 15.0
    }
  ]
}
```

### Category: Activity Summary
**Route:** `/api/v1/metrics/summary`
**Request Type:** GET
**Payload:** None (requires Authorization header)
**Sample Response:**
```json
{
  "total_activities": 100,
  "unique_users": 25,
  "most_active_day": "2024-01-15",
  "most_popular_action": "update_cv",
  "average_activities_per_user": 4.0
}
```

### Category: Comprehensive Metrics
**Route:** `/api/v1/metrics/comprehensive`
**Request Type:** GET
**Payload:** None (requires Authorization header)
**Sample Response:**
```json
{
  "login_frequencies": {
    "daily_logins": [],
    "weekly_logins": [],
    "monthly_logins": []
  },
  "action_frequencies": {
    "action_counts": []
  },
  "popular_actions": {
    "popular_actions": []
  },
  "summary": {
    "total_activities": 100,
    "unique_users": 25,
    "most_active_day": "2024-01-15",
    "most_popular_action": "update_cv",
    "average_activities_per_user": 4.0
  }
}
```

---

## 🔧 Template & Language Support

### Available Templates:
- `standard` - Classic professional template
- `modern` - Contemporary design with clean layout
- `creative` - Artistic template with unique styling
- `german-ausbildung` - Specialized German apprenticeship format

### Supported Languages:
- `en` - English
- `de` - German (Deutsch)
- `ar` - Arabic

### File Categories:
- `photo` - Profile photos
- `certificate` - Educational certificates
- `cover_letter` - Cover letter documents
- `other` - Miscellaneous files

---

## 🚨 Error Responses

All endpoints return consistent error responses:

### Category: Validation Error
**Sample Response:**
```json
{
  "message": "Validation failed",
  "errors": [
    {
      "field": "email",
      "message": "Invalid email format"
    }
  ]
}
```

### Category: Authentication Error
**Sample Response:**
```json
{
  "message": "Authentication required",
  "errors": null
}
```

### Category: Authorization Error
**Sample Response:**
```json
{
  "message": "Access denied",
  "errors": null
}
```

### Category: Not Found Error
**Sample Response:**
```json
{
  "message": "Resource not found",
  "errors": null
}
```

### Category: Server Error
**Sample Response:**
```json
{
  "message": "Internal server error",
  "errors": null
}
```

---

## 📝 Implementation Notes

1. **Authentication**: Store JWT tokens securely (preferably in httpOnly cookies)
2. **Token Refresh**: Implement automatic token refresh before expiration
3. **Error Handling**: Always check response status codes and handle errors appropriately
4. **File Upload**: Use base64 encoding for file uploads
5. **Pagination**: Some endpoints may support pagination parameters (limit, offset)
6. **Rate Limiting**: API has rate limiting - handle 429 responses appropriately
7. **CORS**: API supports CORS for cross-origin requests
8. **Content-Type**: Always set `Content-Type: application/json` for POST/PUT requests
